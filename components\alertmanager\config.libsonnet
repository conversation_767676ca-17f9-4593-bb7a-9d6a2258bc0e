local slackWebhookUrl = "*********************************************************************************";

{
  global: {
    resolve_timeout: '5m',
  },
  inhibit_rules: [
    {
      equal: ['namespace', 'alertname'],
      source_matchers: ['severity = critical'],
      target_matchers: ['severity =~ warning|info'],
    },
    {
      equal: ['namespace', 'alertname'],
      source_matchers: ['severity = warning'],
      target_matchers: ['severity = info'],
    },
    {
      equal: ['namespace'],
      source_matchers: ['alertname = InfoInhibitor'],
      target_matchers: ['severity = info'],
    },
  ],
  receivers: [
    {
      name: 'slack-notifier',
      slack_configs: [
        {
          send_resolved: true,
          channel: '#data-replication-alarms',
          title: '{{ .CommonLabels.alertname }}',
          text: |||
  {{ range .Alerts }}
  {{ if eq .Status "resolved" }}
  ✅ *Alert Resolved*
  
  *Alert Name:* {{ .Labels.alertname }}
  *Severity:* {{ .Labels.severity }}
  *Status:* {{ .Status }}
  *Start Time:* {{ .StartsAt }}
  *End Time:* {{ .EndsAt }}
  *Namespace:* {{ .Labels.namespace }}
  *Instance:* {{ .Labels.pod }}
  *Summary:* {{ .Annotations.summary }}
  *Description:* {{ .Annotations.description }}
  {{ else }}
  🔥 *New Alert*
  
  *Alert Name:* {{ .Labels.alertname }}
  *Severity:* {{ .Labels.severity }}
  *Status:* {{ .Status }}
  *Start Time:* {{ .StartsAt }}
  *Namespace:* {{ .Labels.namespace }}
  *Instance:* {{ .Labels.pod }}
  *Summary:* {{ .Annotations.summary }}
  *Description:* {{ .Annotations.description }}
  {{ end }}
  {{ end }}
|||,
          api_url: slackWebhookUrl,
        },
      ],
    },
    { name: 'Watchdog' },
    { name: 'null' },
  ],
  route: {
    group_by: ['alertname'],
    group_wait: '30s',
    group_interval: '5m',
    repeat_interval: '12h',
    receiver: 'null',
    routes: [
		{ matchers: ['alertname = Watchdog'], receiver: 'Watchdog' },
		{ matchers: ['alertname = InfoInhibitor'], receiver: 'null' },
		{
		  matchers: ['severity =~ warning|critical'],
		  receiver: 'null',
		  routes: [
			{
			  matchers: ['namespace = dart-prod'],
			  receiver: 'slack-notifier',
			  continue: true,
			},
			{
			  matchers: ['namespace = monitoring'],
			  receiver: 'slack-notifier',
			  continue: true,
			},
		  ],
		},
	  ],
  },
}
