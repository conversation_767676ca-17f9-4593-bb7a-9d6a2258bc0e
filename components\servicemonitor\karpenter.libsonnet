{
  apiVersion: 'monitoring.coreos.com/v1',
  kind: 'ServiceMonitor',
  metadata: {
    name: 'karpenter-monitor',
    namespace: 'monitoring',
    labels: {
        release: 'prometheus',
    },
  },
  spec: {
    namespaceSelector: {
      matchNames: ['kube-system'],
    },
    selector: {
      matchLabels: {
        'app.kubernetes.io/name': 'karpenter',
      },
    },
    endpoints: [
      {
        port: 'http-metrics',
        interval: '15s'
      },
    ],
  },
}