{
  apiVersion: 'monitoring.coreos.com/v1',
  kind: 'PodMonitor',
  metadata: {
    name: 'kafka-connect-debezium-monitor',
    namespace: 'monitoring',
    labels: {
      release: 'prometheus',
    },
  },
  spec: {
    namespaceSelector: {
      matchNames: ['dart-dev','dart-prod']
    },
    selector: {
      matchLabels: {
        app: 'kafka-connect-debezium',
      },
    },
    podMetricsEndpoints: [
      {
        portNumber: 5556,
        interval: '30s',
      },
    ],
  },
}
