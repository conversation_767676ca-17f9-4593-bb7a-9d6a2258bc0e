# dart-kube-prometheus

A monitoring system for Dart.

## Overview
This monitoring system is based on [kube-prometheus](https://github.com/prometheus-operator/kube-prometheus), customized for monitoring Dart.

## Installing

Uses [jsonnet-bundler](https://github.com/jsonnet-bundler/jsonnet-bundler#install) for package management.

First-time install:

```shell
$ jb update
$ jb init  # Creates the initial/empty `jsonnetfile.json`
$ jb install github.com/prometheus-operator/kube-prometheus/jsonnet/kube-prometheus@main # Creates `vendor/` & `jsonnetfile.lock.json`, and fills in `jsonnetfile.json`
$ chmod +x build.sh
$ ./build.sh ./main.jsonnet
$ kubectl apply -f manifests/
```

Install after changes:

```shell
$ ./build.sh ./main.jsonnet
$ kubectl apply -f manifests/
```
