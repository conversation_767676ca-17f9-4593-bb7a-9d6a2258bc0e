local alertmanagerConfig = import 'components/alertmanager/config.libsonnet';
local kafkaExporter = import 'components/kafka-exporter/kafka-exporter.jsonnet';
local prometheusSpec = import 'components/prometheus/spec.jsonnet';
local podMonitorKafkaConnectDebezium = import 'components/podmonitors/kafka-connect-debezium.libsonnet';
local podMonitorKafkaConnectSnowflake = import 'components/podmonitors/kafka-connect-snowflake.libsonnet';
local serviceMonitorKarpenter = import 'components/servicemonitor/karpenter.libsonnet';

local kp =
  (import 'kube-prometheus/main.libsonnet') +
  (import 'components/grafana/db.libsonnet')+
  {
    values+:: {
      common+: {
        namespace: 'monitoring',
      },
      alertmanager+: {
        config: alertmanagerConfig,
      },
    },
    alertmanager+:: {
      alertmanager+: {
          spec+: {
            containers: [
              {
                name: 'config-reloader',
                resources: {
                  requests: {
                    cpu: '50m',
                    memory: '50Mi',
                  },
                  limits: {
                    cpu: '100m',
                    memory: '100Mi',
                  },
                },
              },
            ],
          },
      },
    },
  };

local excludeGroups = [
  'kubernetes-system-controller-manager',
  'kubernetes-system-scheduler',
  'windows.node.rules',
  'windows.pod.rules'
];

local filteredKubernetesControlPlane = {
  ['kubernetes-' + name]:
    if name == 'prometheusRule' then
      kp.kubernetesControlPlane.prometheusRule {
        spec+: {
          groups: std.filter(
            function(group) !std.member(excludeGroups, group.name),
            kp.kubernetesControlPlane.prometheusRule.spec.groups
          ),
        }
      }
    else
      kp.kubernetesControlPlane[name]
  for name in std.objectFields(kp.kubernetesControlPlane)
};



local excludeDashboards = [
  'grafana-dashboard-k8s-resources-windows-cluster',
  'grafana-dashboard-k8s-resources-windows-namespace',
  'grafana-dashboard-k8s-resources-windows-pod',
  'grafana-dashboard-k8s-windows-cluster-rsrc-use',
  'grafana-dashboard-k8s-windows-node-rsrc-use',
  'grafana-dashboard-nodes-darwin',
  'grafana-dashboard-nodes-aix'
];

local filteredGrafana = {
  ['grafana-' + name]:
    if name == 'dashboardDefinitions' then
      kp.grafana.dashboardDefinitions {
        items: std.filter(
          function(d) !std.member(excludeDashboards, d.metadata.name),
          kp.grafana.dashboardDefinitions.items
        ),
      }
    else
      kp.grafana[name]
  for name in std.objectFields(kp.grafana)
};

{
  'setup/0namespace-namespace': kp.kubePrometheus.namespace,
} +
{
  ['setup/prometheus-operator-' + name]: kp.prometheusOperator[name]
  for name in std.filter((function(name) name != 'serviceMonitor' && name != 'prometheusRule'), std.objectFields(kp.prometheusOperator))
} +
{ 'prometheus-operator-serviceMonitor': kp.prometheusOperator.serviceMonitor } +
{ 'prometheus-operator-prometheusRule': kp.prometheusOperator.prometheusRule } +
{ 'kube-prometheus-prometheusRule': kp.kubePrometheus.prometheusRule } +
{ ['alertmanager-' + name]: kp.alertmanager[name] for name in std.objectFields(kp.alertmanager) } +
{ ['blackbox-exporter-' + name]: kp.blackboxExporter[name] for name in std.objectFields(kp.blackboxExporter) } +
filteredGrafana+
{ ['kube-state-metrics-' + name]: kp.kubeStateMetrics[name] for name in std.objectFields(kp.kubeStateMetrics) } +
filteredKubernetesControlPlane +
{ ['node-exporter-' + name]: kp.nodeExporter[name] for name in std.objectFields(kp.nodeExporter) } +
{ 
  ['prometheus-' + name]: 
    if name == 'prometheus' then 
      (kp.prometheus[name] + prometheusSpec.prometheusCustomSpec) 
    else 
      kp.prometheus[name]
  for name in std.objectFields(kp.prometheus)
} +
{ ['prometheus-adapter-' + name]: kp.prometheusAdapter[name] for name in std.objectFields(kp.prometheusAdapter) } +
kafkaExporter +
{
  ['podmonitor-kafka-connect-debezium']: podMonitorKafkaConnectDebezium,
} +
{
  ['podmonitor-kafka-connect-snowflake']: podMonitorKafkaConnectSnowflake,
} +
{
  ['service-monitor-kafka-karpenter']: serviceMonitorKarpenter,
}