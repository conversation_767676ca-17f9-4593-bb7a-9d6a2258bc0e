{
  prometheusCustomSpec: {
    spec+: {
      retention: '14d',
      resources+: {
        requests: {
          cpu: '100m',
          memory: '600Mi',
        },
        limits: {
          cpu: '500m',
          memory: '1Gi',
        },
      },
      containers: [
          {
            name: 'config-reloader',
            resources: {
              requests: {
                cpu: '50m',
                memory: '50Mi',
              },
              limits: {
                cpu: '100m',
                memory: '100Mi',
              },
            },
          },
        ],
      storage: {
        volumeClaimTemplate: {
          spec: {
            accessModes: ['ReadWriteOnce'],
            storageClassName: 'gp3',
            resources: {
              requests: {
                storage: '65Gi',
              },
            },
          },
        },
      },
    },
  },
}
