{
  // Kafka Exporter Deployment
  'kafka-exporter-deployment': {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'kafka-exporter',
      namespace: 'monitoring',
      labels: {
        app: 'kafka-exporter',
      },
    },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: {
          app: 'kafka-exporter',
        },
      },
      template: {
        metadata: {
          labels: {
            app: 'kafka-exporter',
          },
        },
        spec: {
          containers: [
            {
              name: 'kafka-exporter',
              image: 'danielqsj/kafka-exporter:latest',
              args: [
				  "--kafka.server",
				  "kafka.dart-prod.svc.cluster.local:9092",
				],
              ports: [
                {
                  containerPort: 9308,
                  name: 'metrics',
                },
              ],
            },
          ],
        },
      },
    },
  },

  // Kafka Exporter Service
  'kafka-exporter-service': {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'kafka-exporter',
      namespace: 'monitoring',
      labels: {
        app: 'kafka-exporter',
      },
    },
    spec: {
      selector: {
        app: 'kafka-exporter',
      },
      ports: [
        {
          name: 'metrics',
          port: 9308,
          targetPort: 'metrics',
        },
      ],
    },
  },

  // Kafka Exporter ServiceMonitor
  'kafka-exporter-servicemonitor': {
    apiVersion: 'monitoring.coreos.com/v1',
    kind: 'ServiceMonitor',
    metadata: {
      name: 'kafka-exporter',
      namespace: 'monitoring',
      labels: {
        release: 'prometheus',
      },
    },
    spec: {
      selector: {
        matchLabels: {
          app: 'kafka-exporter',
        },
      },
      namespaceSelector: {
        matchNames: ['monitoring'],
      },
      endpoints: [
        {
          port: 'metrics',
          interval: '15s',
        },
      ],
    },
  },
}
